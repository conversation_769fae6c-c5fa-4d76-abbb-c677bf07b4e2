package com.illumio.data.model.constants;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.response.DestinationRoleLevelTrafficResponse;
import com.illumio.data.response.DoraCriticalIctResponse;
import com.illumio.data.response.ExternalDataTransferResourceInsightsResponse;
import com.illumio.data.response.ExternalDestinationCategoryResponse;
import com.illumio.data.response.ExternalGeoTransferResponse;
import com.illumio.data.response.ExternalServiceResponse;
import com.illumio.data.response.IpDataTransferResponse;
import com.illumio.data.response.IpTopResourcesResponse;
import com.illumio.data.response.IpTopRolesResponse;
import com.illumio.data.response.LLMinUseResponse;
import com.illumio.data.response.MaliciousIpTrafficResourceInsightsResponse;
import com.illumio.data.response.ResourceInsightsResponse;
import com.illumio.data.response.RiskyServicesTrafficResourceInsightsResponse;
import com.illumio.data.response.RiskyServicesTrafficResponse;
import com.illumio.data.response.RiskyTrafficByRolesResourceInsightsResponse;
import com.illumio.data.response.ThirdPartyInboundResponse;
import com.illumio.data.response.ThirdPartyOutboundResponse;
import com.illumio.data.response.ThreatMapResponse;
import com.illumio.data.response.TopCategoryWithLlmResponse;
import com.illumio.data.response.TopCategoryWithMaliciousIpResponse;
import com.illumio.data.response.TopCrossRegionResponse;
import com.illumio.data.response.TopDestinationRolesResponse;
import com.illumio.data.response.TopIctResponse;
import com.illumio.data.response.TopMaliciousIpsResponse;
import com.illumio.data.response.TopRegionToCountryResponse;
import com.illumio.data.response.TopRolesResponse;
import com.illumio.data.response.TopServicesResponse;
import com.illumio.data.response.TopSourceTransferResponse;
import com.illumio.data.response.TopSourcesWithLlmResponse;
import com.illumio.data.response.TopSoureRoleTransferResponse;
import com.illumio.data.response.TopWorkloadsResponse;
import com.illumio.data.response.UnencryptedServiceResponse;
import com.illumio.data.response.ZoneLevelTrafficResponse;
import jakarta.annotation.PostConstruct;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
@Component
public class ResponseBuilderRegistry {

    private final Map<String, ResponseBuilder<?>> registry = new HashMap<>();

    private final ZoneLevelTrafficResponse zoneLevelTrafficResponse;
    private final RiskyServicesTrafficResponse riskyServicesTrafficResponse;
    private final TopDestinationRolesResponse topDestinationRolesResponse;
    private final DestinationRoleLevelTrafficResponse destinationRoleLevelTrafficResponse;
    private final TopWorkloadsResponse topWorkloadsResponse;
    private final ResourceInsightsResponse resourceInsightsResponse;
    private final TopMaliciousIpsResponse topMaliciousIpsResponse;
    private final ThreatMapResponse threatMapResponse;
    private final TopCategoryWithMaliciousIpResponse topCategoryWithMaliciousIpResponse;
    private final TopRolesResponse topRolesResponse;
    private final TopServicesResponse topServicesResponse;
    private final ExternalDestinationCategoryResponse externalDestinationCategoryResponse;
    private final TopSoureRoleTransferResponse topSoureRoleTransferResponse;
    private final ExternalGeoTransferResponse externalGeoTransferResponse;
    private final TopSourceTransferResponse topSourceTransferResponse;
    private final ExternalServiceResponse externalServiceResponse;
    private final LLMinUseResponse llmInUseResponse;
    private final TopCategoryWithLlmResponse topCategoryWithLlmResponse;
    private final TopSourcesWithLlmResponse topSourcesWithLlmResponse;
    private final ThirdPartyInboundResponse thirdPartyInboundResponse;
    private final ThirdPartyOutboundResponse thirdPartyOutboundResponse;
    private final TopCrossRegionResponse topCrossRegionResponse;
    private final TopRegionToCountryResponse topRegionToCountryResponse;
    private final UnencryptedServiceResponse unencryptedServiceResponse;
    private final TopIctResponse topIctResponse;
    private final DoraCriticalIctResponse doraCriticalIctResponse;
    private final RiskyTrafficByRolesResourceInsightsResponse riskyTrafficByRolesResourceInsightsResponse;
    private final MaliciousIpTrafficResourceInsightsResponse maliciousIpTrafficResourceInsightsResponse;
    private final ExternalDataTransferResourceInsightsResponse externalDataTransferResourceInsightsResponse;
    private final RiskyServicesTrafficResourceInsightsResponse riskyServicesTrafficResourceInsightsResponse;
    private final IpTopResourcesResponse ipTopResourcesResponse;
    private final IpTopRolesResponse ipTopRolesResponse;
    private final IpDataTransferResponse ipDataTransferResponse;

    @PostConstruct
    private void registerBuilders() {
        registry.put(WidgetId.ZONE_LEVEL_TRAFFIC, zoneLevelTrafficResponse);
        registry.put(WidgetId.RISKY_SERVICES_TRAFFIC_RESOURCE_INSIGHTS, riskyServicesTrafficResourceInsightsResponse);
        registry.put(WidgetId.TOP_DESTINATION_ROLES, topDestinationRolesResponse);
        registry.put(WidgetId.DESTINATION_ROLE_LEVEL_TRAFFIC, destinationRoleLevelTrafficResponse);
        registry.put(WidgetId.TOP_WORKLOADS, topWorkloadsResponse);
        registry.put(WidgetId.RESOURCE_INSIGHTS, resourceInsightsResponse);
        registry.put(WidgetId.TOP_MALICIOUS_IPS, topMaliciousIpsResponse);
        registry.put(WidgetId.THREAT_MAP, threatMapResponse);
        registry.put(WidgetId.TOP_CATEGORY_WITH_MALICIOUS_IP, topCategoryWithMaliciousIpResponse);
        registry.put(WidgetId.TOP_ROLES, topRolesResponse);
        registry.put(WidgetId.TOP_SERVICES, topServicesResponse);
        registry.put(WidgetId.EXTERNAL_DESTINATION_CATEGORY, externalDestinationCategoryResponse);
        registry.put(WidgetId.TOP_SOURCE_ROLE_TRANSFER, topSoureRoleTransferResponse);
        registry.put(WidgetId.EXTERNAL_GEO_TRANSFER, externalGeoTransferResponse);
        registry.put(WidgetId.TOP_SOURCE_TRANSFER, topSourceTransferResponse);
        registry.put(WidgetId.EXTERNAL_SERVICE_TRANSFER, externalServiceResponse);
        registry.put(WidgetId.LLM_IN_USE, llmInUseResponse);
        registry.put(WidgetId.TOP_CATEGORY_WITH_LLM, topCategoryWithLlmResponse);
        registry.put(WidgetId.TOP_SOURCES_WITH_LLM, topSourcesWithLlmResponse);
        registry.put(WidgetId.THIRD_PARTY_DEPENDENCY_INBOUND, thirdPartyInboundResponse);
        registry.put(WidgetId.THIRD_PARTY_DEPENDENCY_OUTBOUND, thirdPartyOutboundResponse);
        registry.put(WidgetId.TOP_CROSS_REGION_TRAFFIC, topCrossRegionResponse);
        registry.put(WidgetId.TOP_REGION_TO_COUNTRY_TRAFFIC, topRegionToCountryResponse);
        registry.put(WidgetId.UNENCRYPTED_SERVICES, unencryptedServiceResponse);
        registry.put(WidgetId.DORA_TOP_ICT, topIctResponse);
        registry.put(WidgetId.DORA_CRITICAL_ICT, doraCriticalIctResponse);
        registry.put(WidgetId.RISKY_TRAFFIC_BY_ROLES_RESOURCE_INSIGHTS, riskyTrafficByRolesResourceInsightsResponse);
        registry.put(WidgetId.MALICIOUS_IP_TRAFFIC_RESOURCE_INSIGHTS, maliciousIpTrafficResourceInsightsResponse);
        registry.put(WidgetId.EXTERNAL_DATA_TRANSFER_RESOURCE_INSIGHTS, externalDataTransferResourceInsightsResponse);
        registry.put(WidgetId.RISKY_SERVICE_TRAFFIC, riskyServicesTrafficResponse);
        registry.put(WidgetId.RISKY_SERVICE_TRAFFIC_INSIGHTS_HUB, riskyServicesTrafficResponse);
        registry.put(WidgetId.TOP_DESTINATION_ROLES_INSIGHTS_HUB, topDestinationRolesResponse);
        registry.put(WidgetId.TOP_SOURCE_TRANSFER_INSIGHTS_HUB, topSourceTransferResponse);
        registry.put(WidgetId.TOP_MALICIOUS_IPS_INSIGHTS_HUB, topMaliciousIpsResponse);
        registry.put(WidgetId.IP_TOP_DESTINATION_RESOURCES, ipTopResourcesResponse);
        registry.put(WidgetId.IP_TOP_SOURCE_RESOURCES, ipTopResourcesResponse);
        registry.put(WidgetId.IP_TOP_DESTINATION_ROLES, ipTopRolesResponse);
        registry.put(WidgetId.IP_TOP_SOURCE_ROLES, ipTopRolesResponse);
        registry.put(WidgetId.IP_DATA_TRANSFER_INBOUND, ipDataTransferResponse);
        registry.put(WidgetId.IP_DATA_TRANSFER_OUTBOUND, ipDataTransferResponse);
    }

    @SuppressWarnings("unchecked")
    public <R> ResponseBuilder<R> getResponseBuilder(String widgetId) {
        Object builder = registry.get(widgetId);
        if (builder == null) {
            throw new IllegalArgumentException("No response builder registered for widgetId: " + widgetId);
        }
        return (ResponseBuilder<R>) builder;
    }

}
