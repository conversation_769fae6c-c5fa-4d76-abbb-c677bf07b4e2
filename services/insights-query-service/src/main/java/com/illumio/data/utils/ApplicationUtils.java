package com.illumio.data.utils;

import com.illumio.data.model.Filters;
import com.illumio.data.model.constants.TrafficDirection;

import java.util.List;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.TRAFFIC_DIRECTION;

public class ApplicationUtils {
    public static TrafficDirection getTrafficDirection(List<Filters> filters) {
        return Optional.ofNullable(filters)
                .flatMap(fs -> fs.stream()
                        .filter(f -> TRAFFIC_DIRECTION.getFieldKey().equalsIgnoreCase(f.getCategoryName()))
                        .map(Filters::getCategoryValue)
                        .flatMap(list -> list.stream().map(Object::toString))
                        .findFirst()
                )
                .map(TrafficDirection::fromString)
                .orElse(TrafficDirection.NONE);
    }
}
