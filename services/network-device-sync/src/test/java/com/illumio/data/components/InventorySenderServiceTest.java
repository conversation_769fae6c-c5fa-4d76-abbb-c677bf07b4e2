package com.illumio.data.components;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.illumio.data.configuration.NetworkDeviceSyncConfig;
import com.illumio.data.model.*;
import org.apache.kafka.common.header.Header;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.test.StepVerifier;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static com.illumio.data.util.InventoryCommonsConstants.INVENTORY_OBJECT_TYPE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InventorySenderServiceTest {

    @Mock
    private KafkaSender<String, String> kafkaInventorySender;

    @Mock
    private NetworkDeviceSyncConfig networkDeviceSyncConfig;

    @Mock
    private NetworkDeviceSyncConfig.KafkaProducerConfig kafkaProducerConfig;

    private final ObjectMapper objectMapper = new ObjectMapper();

    private InventorySenderService inventorySenderService;

    @BeforeEach
    void setup() {
        objectMapper.registerModule(new JavaTimeModule());
        inventorySenderService = new InventorySenderService(kafkaInventorySender, networkDeviceSyncConfig, objectMapper);
    }

    @Test
    void sendInventoryObjects_shouldSendAllValidRecords() throws JsonProcessingException {
        String tenantId = UUID.randomUUID().toString();

        InventoryItem srcDevice = new InventoryItem(
                tenantId, "************", InventoryObjectType.DEVICE,
                InventoryItemType.DEVICE, "{\"hostname\": \"host-01\"}");
        InventoryItem destDevice = new InventoryItem(
                tenantId, "************", InventoryObjectType.DEVICE,
                InventoryItemType.DEVICE, "{\"hostname\": \"host-02\"}");
        InventoryItem firewall = new InventoryItem(
                tenantId, "firewall-name", InventoryObjectType.DEVICE,
                InventoryItemType.FIREWALL, "{\"macAddress\": \"00-14-22-01-23-45\"}");
        InventoryItem srcIdentity = new InventoryItem(
                tenantId, "<EMAIL>", InventoryObjectType.IDENTITY,
                InventoryItemType.USER, "{\"userName\": \"<EMAIL>\"}");
        InventoryItem destIdentity = new InventoryItem(
                tenantId, "<EMAIL>", InventoryObjectType.IDENTITY,
                InventoryItemType.USER, "{\"userName\": \"<EMAIL>\"}");

        FlowInventory flowInventory = new FlowInventory();
        flowInventory.setInventoryItems(List.of(srcDevice, destDevice, firewall, srcIdentity, destIdentity));
        flowInventory.setDeviceToDeviceRelationships(List.of(
                DeviceToDeviceRelationship.builder().srcDeviceId(srcDevice.getId()).destDeviceId(destDevice.getId()).build(),
                DeviceToDeviceRelationship.builder().srcDeviceId(srcDevice.getId()).destDeviceId(firewall.getId()).build(),
                DeviceToDeviceRelationship.builder().srcDeviceId(firewall.getId()).destDeviceId(destDevice.getId()).build()));
        flowInventory.setIdentityToDeviceRelationships(List.of(
                IdentityToDeviceRelationship.builder().deviceId(srcDevice.getId()).identityId(srcIdentity.getId()).build(),
                IdentityToDeviceRelationship.builder().deviceId(destDevice.getId()).identityId(destIdentity.getId()).build()));

        when(networkDeviceSyncConfig.getKafkaInventoryProducerConfig()).thenReturn(kafkaProducerConfig);
        when(kafkaProducerConfig.getTopic()).thenReturn("inventory-topic");
        ArgumentCaptor<Flux<SenderRecord<String, String, String>>> captor = ArgumentCaptor.forClass(Flux.class);
        when(kafkaInventorySender.send(any())).thenReturn(Flux.empty());

        Mono<Void> result = inventorySenderService.sendInventoryObjects(flowInventory);
        StepVerifier.create(result).verifyComplete();

        verify(kafkaInventorySender).send(captor.capture());

        List<SenderRecord<String, String, String>> records = captor.getValue().collectList().block();
        assertThat(records).hasSize(10);

        InventoryObjectType[] expectedHeaders = new InventoryObjectType[] {
                InventoryObjectType.DEVICE,
                InventoryObjectType.DEVICE,
                InventoryObjectType.DEVICE,
                InventoryObjectType.IDENTITY,
                InventoryObjectType.IDENTITY,
                InventoryObjectType.DEVICE_DEVICE_RELATIONSHIP,
                InventoryObjectType.DEVICE_DEVICE_RELATIONSHIP,
                InventoryObjectType.DEVICE_DEVICE_RELATIONSHIP,
                InventoryObjectType.IDENTITY_DEVICE_RELATIONSHIP,
                InventoryObjectType.IDENTITY_DEVICE_RELATIONSHIP
        };
        Object[] expectedItems = new Object[] {
                srcDevice, destDevice, firewall, srcIdentity, destIdentity,
                flowInventory.getDeviceToDeviceRelationships().get(0),
                flowInventory.getDeviceToDeviceRelationships().get(1),
                flowInventory.getDeviceToDeviceRelationships().get(2),
                flowInventory.getIdentityToDeviceRelationships().get(0),
                flowInventory.getIdentityToDeviceRelationships().get(1)
        };
        String[] expectedKeys = Arrays.stream(expectedItems)
                .map(item -> getPartitionKey(item, tenantId))
                .toArray(String[]::new);

        for (int i = 0; i < records.size(); i++) {
            SenderRecord<String, String, String> record = records.get(i);
            assertThat(record.topic()).isEqualTo("inventory-topic");
            assertThat(record.key()).isEqualTo(expectedKeys[i]);

            Header header = record.headers().lastHeader(INVENTORY_OBJECT_TYPE);
            assertThat(header).isNotNull();
            assertEquals(expectedHeaders[i].name(), new String(header.value()));
            assertEquals(objectMapper.writeValueAsString(expectedItems[i]), record.value());
        }
    }

    @Test
    void sendInventoryObjects_shouldNotSend_OnEmptyFlows() {
        FlowInventory flowInventory = new FlowInventory();
        flowInventory.setInventoryItems(Collections.emptyList());
        flowInventory.setDeviceToDeviceRelationships(Collections.emptyList());
        flowInventory.setIdentityToDeviceRelationships(Collections.emptyList());

        Mono<Void> result = inventorySenderService.sendInventoryObjects(flowInventory);

        StepVerifier.create(result).verifyComplete();
        verifyNoInteractions(kafkaInventorySender);
    }

    private String getPartitionKey(final Object inventoryObject, final String tenantId) {
        if (inventoryObject instanceof InventoryItem inventoryItem) {
            return tenantId + "," + inventoryItem.getId();
        } else if (inventoryObject instanceof DeviceToDeviceRelationship deviceToDeviceRelationship) {
            return tenantId + "," + deviceToDeviceRelationship.getSrcDeviceId();
        } else if (inventoryObject instanceof IdentityToDeviceRelationship identityToDeviceRelationship) {
            return tenantId + "," + identityToDeviceRelationship.getDeviceId();
        } else {
            return null;
        }
    }

}
