package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.annotations.retry.RetryReactiveOnError;
import com.illumio.data.configuration.NetworkDeviceSyncConfig;
import com.illumio.data.model.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.illumio.data.util.InventoryCommonsConstants.INVENTORY_OBJECT_TYPE;

@Slf4j
@Service
@RequiredArgsConstructor
public class InventorySenderService {

    private final KafkaSender<String, String> kafkaInventorySender;
    private final NetworkDeviceSyncConfig networkDeviceSyncConfig;
    private final ObjectMapper objectMapper;

    @RetryReactiveOnError
    public Mono<Void> sendInventoryObjects(final FlowInventory flowInventory) {
        final Optional<String> tenantIdOpt = getTenantId(flowInventory);
        return tenantIdOpt.map(tenantId ->
                kafkaInventorySender.send(createRecords(flowInventory, tenantId))
                                    .doOnComplete(() ->
                                            log.debug("Successfully pushed flowInventory={} to Kafka", flowInventory))
                                    .then())
                          .orElseGet(Mono::empty);
    }

    private Flux<SenderRecord<String, String, String>> createRecords(final FlowInventory flowInventory, final String tenantId) {
        final List<SenderRecord<String, String, String>> records = new ArrayList<>(
                flowInventory.getInventoryItems()
                             .stream()
                             .map(inventoryItem -> createRecord(
                                     getPartitionKey(inventoryItem, tenantId),
                                     inventoryItem.getSuperType(),
                                     inventoryItem))
                             .toList());
        records.addAll(
                flowInventory.getDeviceToDeviceRelationships()
                             .stream()
                             .map(deviceToDeviceRelationship -> createRecord(
                                     getPartitionKey(deviceToDeviceRelationship, tenantId),
                                     InventoryObjectType.DEVICE_DEVICE_RELATIONSHIP,
                                     deviceToDeviceRelationship))
                             .toList());
        records.addAll(
                flowInventory.getIdentityToDeviceRelationships()
                             .stream()
                             .map(identityToDeviceRelationship -> createRecord(
                                     getPartitionKey(identityToDeviceRelationship, tenantId),
                                     InventoryObjectType.IDENTITY_DEVICE_RELATIONSHIP,
                                     identityToDeviceRelationship))
                             .toList());
        return Flux.fromIterable(records);
    }

    @SneakyThrows
    private SenderRecord<String, String, String> createRecord(final String partitionKey,
                                                              final InventoryObjectType inventoryObjectType,
                                                              final Object messageObj) {
        final String message = objectMapper.writeValueAsString(messageObj);
        final String kafkaTopic = networkDeviceSyncConfig.getKafkaInventoryProducerConfig().getTopic();
        final ProducerRecord<String, String> producerRecord = new ProducerRecord<>(kafkaTopic, partitionKey, message);
        producerRecord.headers().add(INVENTORY_OBJECT_TYPE, inventoryObjectType.name().getBytes());
        return SenderRecord.create(producerRecord, null);
    }

    private Optional<String> getTenantId(final FlowInventory flowInventory) {
        return flowInventory.getInventoryItems().stream().findFirst()
                            .map(InventoryItem::getTenantId)
                            .map(UUID::toString);
    }

    private String getPartitionKey(final InventoryObject inventoryObject, final String tenantId) {
        final String partitionKeySecondComponent;
        if (inventoryObject instanceof InventoryItem inventoryItem) {
            partitionKeySecondComponent = inventoryItem.getId().toString();
        } else if (inventoryObject instanceof DeviceToDeviceRelationship deviceToDeviceRelationship) {
            partitionKeySecondComponent = deviceToDeviceRelationship.getSrcDeviceId().toString();
        } else if (inventoryObject instanceof IdentityToDeviceRelationship identityToDeviceRelationship) {
            partitionKeySecondComponent = identityToDeviceRelationship.getDeviceId().toString();
        } else {
            throw new IllegalArgumentException("Partition key is not implemented for InventoryObject subtype.");
        }
        return "%s,%s".formatted(tenantId, partitionKeySecondComponent);
    }

}
