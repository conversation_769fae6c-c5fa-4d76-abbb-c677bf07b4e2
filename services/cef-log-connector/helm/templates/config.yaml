apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "CefLogConnector.fullname" . }}-env-configmap
  labels:
    {{- include "CefLogConnector.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
        org:
          apache:
            kafka: {{.Values.logging.level.kafka}}
    spring:
      application:
        name: "cef-log-connector"
      output:
        ansi:
          enabled: ALWAYS
      main:
        web-application-type: none
    
    cef-log-connector:
      log-processing-mode: "{{.Values.cefLogConnector.logProcessingMode}}"
    eventhub:
      password: "{{.Values.eventhub.password}}"
      kafka-consumer-config:
        bootstrapServers: "{{.Values.cefLogConnector.kafkaConsumerConfig.bootstrapServers}}"
        isConnectionString: "{{.Values.cefLogConnector.kafkaConsumerConfig.isConnectionString}}"
        isManagedIdentity: "{{.Values.cefLogConnector.kafkaConsumerConfig.isManagedIdentity}}"
        topic: "{{.Values.cefLogConnector.kafkaConsumerConfig.topic}}"
        groupId: "{{.Values.cefLogConnector.kafkaConsumerConfig.groupId}}"
        autoOffsetReset: "{{.Values.cefLogConnector.kafkaConsumerConfig.autoOffsetReset}}"
        requestTimeoutMs: "{{.Values.cefLogConnector.kafkaConsumerConfig.requestTimeoutMs}}"
        maxPollRecords: "{{.Values.cefLogConnector.kafkaConsumerConfig.maxPollRecords}}"
        maxPartitionFetchBytes: "{{.Values.cefLogConnector.kafkaConsumerConfig.maxPartitionFetchBytes}}"
        partitions: {{.Values.cefLogConnector.kafkaConsumerConfig.partitions}}
        backPressureEvents: "{{.Values.cefLogConnector.kafkaConsumerConfig.backPressureEvents}}"
        prefetchCount: "{{.Values.cefLogConnector.kafkaConsumerConfig.prefetchCount}}"
        concurrency: "{{.Values.cefLogConnector.kafkaConsumerConfig.concurrency}}"
        loadBalancingStrategy: "{{.Values.cefLogConnector.kafkaConsumerConfig.loadBalancingStrategy}}"
      kafka-producer-config:
        bootstrapServers: "{{.Values.cefLogConnector.kafkaProducerConfig.bootstrapServers}}"
        isConnectionString: "{{.Values.cefLogConnector.kafkaProducerConfig.isConnectionString}}"
        isManagedIdentity: "{{.Values.cefLogConnector.kafkaProducerConfig.isManagedIdentity}}"
        kafkaFlowTopic: "{{.Values.cefLogConnector.kafkaProducerConfig.kafkaFlowTopic}}"
        kafkaCefJsonTopic: "{{.Values.cefLogConnector.kafkaProducerConfig.kafkaCefJsonTopic}}"
        requestTimeoutMs: "{{.Values.cefLogConnector.kafkaProducerConfig.requestTimeoutMs}}"
        deliveryTimeoutMs: "{{.Values.cefLogConnector.kafkaProducerConfig.deliveryTimeoutMs}}"
        lingerMs: "{{.Values.cefLogConnector.kafkaProducerConfig.lingerMs}}"
        batchSize: "{{.Values.cefLogConnector.kafkaProducerConfig.batchSize}}"
        bufferMemory: "{{.Values.cefLogConnector.kafkaProducerConfig.bufferMemory}}"
        maxBlockMs: "{{.Values.cefLogConnector.kafkaProducerConfig.maxBlockMs}}"
        senderMaxAttempts: "{{.Values.cefLogConnector.kafkaProducerConfig.senderMaxAttempts}}"
        senderBackoff: "{{.Values.cefLogConnector.kafkaProducerConfig.senderBackoff}}"
        maxInFlight: {{.Values.cefLogConnector.kafkaProducerConfig.maxInFlight}}
      storageAccountConfig:
        endpoint: "{{.Values.cefLogConnector.storageAccountConfig.endpoint}}"
        containerName: "{{.Values.cefLogConnector.storageAccountConfig.containerName}}"
        isConnectionString: "{{.Values.cefLogConnector.storageAccountConfig.isConnectionString}}"
        isManagedIdentity: "{{.Values.cefLogConnector.storageAccountConfig.isManagedIdentity}}"
      tenantConfig:
        tenantId: "{{.Values.cefLogConnector.tenantConfig.tenantId}}"
      samplerConfig:
        isEnabled: "{{.Values.cefLogConnector.samplerConfig.isEnabled}}"
        batchSampleSize: "{{.Values.cefLogConnector.samplerConfig.batchSampleSize}}"
