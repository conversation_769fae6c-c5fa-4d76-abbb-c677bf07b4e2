package com.illumio.data.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.*;

public class IpUtilsTest {

    @Test
    void testPrivateIPv4Addresses_ShouldReturnTrue() {
        // Site-local addresses (RFC 1918)
        assertTrue(IpUtils.isPrivateIP("********"), "******** should be private");
        assertTrue(IpUtils.isPrivateIP("**************"), "************** should be private");
        assertTrue(IpUtils.isPrivateIP("**********"), "********** should be private");
        assertTrue(IpUtils.isPrivateIP("**************"), "************** should be private");
        assertTrue(IpUtils.isPrivateIP("***********"), "*********** should be private");
        assertTrue(IpUtils.isPrivateIP("***************"), "*************** should be private");
        
        // Loopback addresses
        assertTrue(IpUtils.isPrivateIP("127.0.0.1"), "127.0.0.1 should be private (loopback)");
        assertTrue(IpUtils.isPrivateIP("***************"), "*************** should be private (loopback)");
        
        // Link-local addresses (RFC 3927)
        assertTrue(IpUtils.isPrivateIP("***********"), "*********** should be private (link-local)");
        assertTrue(IpUtils.isPrivateIP("***************"), "*************** should be private (link-local)");
    }

    @Test
    void testPublicIPv4Addresses_ShouldReturnFalse() {
        assertFalse(IpUtils.isPrivateIP("*******"), "******* should be public");
        assertFalse(IpUtils.isPrivateIP("*******"), "******* should be public");
        assertFalse(IpUtils.isPrivateIP("**************"), "************** should be public");
        assertFalse(IpUtils.isPrivateIP("*************"), "************* should be public");
        assertFalse(IpUtils.isPrivateIP("***************"), "*************** should be public");
        
        // Edge cases around private ranges
        assertFalse(IpUtils.isPrivateIP("*************"), "************* should be public (just before 10.x.x.x)");
        assertFalse(IpUtils.isPrivateIP("********"), "******** should be public (just after 10.x.x.x)");
        assertFalse(IpUtils.isPrivateIP("**************"), "************** should be public (just before 172.16.x.x)");
        assertFalse(IpUtils.isPrivateIP("**********"), "********** should be public (just after 172.31.x.x)");
        assertFalse(IpUtils.isPrivateIP("***************"), "*************** should be public (just before 192.168.x.x)");
        assertFalse(IpUtils.isPrivateIP("***********"), "*********** should be public (just after 192.168.x.x)");
    }

    @Test
    void testPrivateIPv6Addresses_ShouldReturnTrue() {
        // IPv6 loopback
        assertTrue(IpUtils.isPrivateIP("::1"), "::1 should be private (IPv6 loopback)");
        assertTrue(IpUtils.isPrivateIP("0:0:0:0:0:0:0:1"), "0:0:0:0:0:0:0:1 should be private (IPv6 loopback)");
        
        // IPv6 link-local addresses (fe80::/10)
        assertTrue(IpUtils.isPrivateIP("fe80::1"), "fe80::1 should be private (IPv6 link-local)");
        assertTrue(IpUtils.isPrivateIP("fe80:0000:0000:0000:0000:0000:0000:0001"), 
                  "fe80:0000:0000:0000:0000:0000:0000:0001 should be private (IPv6 link-local)");
        
        // IPv6 unique local addresses (fc00::/7) - site-local
        assertTrue(IpUtils.isPrivateIP("fc00::1"), "fc00::1 should be private (IPv6 unique local)");
        assertTrue(IpUtils.isPrivateIP("fd00::1"), "fd00::1 should be private (IPv6 unique local)");
    }

    @Test
    void testPublicIPv6Addresses_ShouldReturnFalse() {
        // Public IPv6 addresses
        assertFalse(IpUtils.isPrivateIP("2001:4860:4860::8888"), "2001:4860:4860::8888 should be public (Google DNS)");
        assertFalse(IpUtils.isPrivateIP("2606:4700:4700::1111"), "2606:4700:4700::1111 should be public (Cloudflare DNS)");
        assertFalse(IpUtils.isPrivateIP("2001:db8::1"), "2001:db8::1 should be public (documentation range but not private)");
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "",                    // Empty string
        "invalid.ip.address",  // Invalid format
        "256.256.256.256",     // Out of range IPv4
        "192.168.1",           // Incomplete IPv4
        "***********.1",       // Too many octets
        "192.168.1.256",       // Invalid octet value
        "not.an.ip",           // Clearly not an IP
        "192.168.1.-1",        // Negative value
        "192.168.1.abc",       // Non-numeric
        ":::",                 // Invalid IPv6
        "gggg::1",             // Invalid IPv6 hex
        "2001:db8::1::2"       // Invalid IPv6 format
    })
    void testInvalidIPAddresses_ShouldReturnFalse(String invalidIP) {
        assertFalse(IpUtils.isPrivateIP(invalidIP), 
                   String.format("'%s' should return false (invalid IP)", invalidIP));
    }

    @Test
    void testNullInput_ShouldReturnFalse() {
        assertFalse(IpUtils.isPrivateIP(null), "null input should return false");
    }

    @Test
    void testSpecialIPv4Addresses() {
        // Test broadcast address
        assertFalse(IpUtils.isPrivateIP("***************"), "*************** should be public (broadcast)");
        
        // Test network addresses
        assertTrue(IpUtils.isPrivateIP("10.0.0.0"), "10.0.0.0 should be private (network address)");
        assertTrue(IpUtils.isPrivateIP("***********"), "*********** should be private (network address)");
        assertTrue(IpUtils.isPrivateIP("**********"), "********** should be private (network address)");
        
        // Test zero address
        assertFalse(IpUtils.isPrivateIP("0.0.0.0"), "0.0.0.0 should be public (any address)");
    }

    @Test
    void testSpecialIPv6Addresses() {
        // Test IPv6 any address
        assertFalse(IpUtils.isPrivateIP("::"), ":: should be public (IPv6 any address)");
        assertFalse(IpUtils.isPrivateIP("0:0:0:0:0:0:0:0"), "0:0:0:0:0:0:0:0 should be public (IPv6 any address)");
    }

    @Test
    void testMixedCaseAndWhitespace() {
        // Test that the method handles different formats correctly
        assertTrue(IpUtils.isPrivateIP("***********"), "Standard format should work");
        
        // Note: InetAddress.getByName() should handle these cases, but let's test edge cases
        // that might be passed to the method
        assertFalse(IpUtils.isPrivateIP(" *********** "), "IP with spaces should return false");
        assertFalse(IpUtils.isPrivateIP("*********** extra"), "IP with extra text should return false");
    }
}
